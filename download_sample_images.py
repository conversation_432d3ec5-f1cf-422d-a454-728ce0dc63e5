#!/usr/bin/env python3
"""
下载示例测试图像
"""

import os
import urllib.request
from pathlib import Path

def download_sample_images():
    """下载一些示例图像用于测试"""
    
    # 创建目录
    Path('sample/Test').mkdir(parents=True, exist_ok=True)
    
    # 一些免费的示例图像URL
    sample_urls = [
        ("https://picsum.photos/512/512?random=1", "sample1.jpg"),
        ("https://picsum.photos/512/512?random=2", "sample2.jpg"), 
        ("https://picsum.photos/512/512?random=3", "sample3.jpg"),
    ]
    
    print("正在下载示例测试图像...")
    
    for url, filename in sample_urls:
        filepath = f"sample/Test/{filename}"
        try:
            print(f"下载: {filename}")
            urllib.request.urlretrieve(url, filepath)
            print(f"✓ 完成: {filename}")
        except Exception as e:
            print(f"✗ 失败: {filename} - {e}")
    
    print("\n示例图像下载完成！")
    print("现在可以运行: python test_pretrained.py")

if __name__ == "__main__":
    download_sample_images()
