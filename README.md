# From-Image-to-Imuge-Immunized-Image-Generation
From Image to Imuge: Immunized Image Generation, official code, implemented by PyTorch, ACMMM 2021 paper

ACMMM 2021 (<PERSON><PERSON>), Chengdu, China

We have just released the source code of the submitted supplementary material to ACMMM 2021. Feel free to contact <NAME_EMAIL> if you encounter any problem during using this project.

## What is image immunization?

For simplicity, you can view it as a robust watermarking scheme for image protection.

Digital images are vulnerable to nefarious tampering attacks such as content addition or removal that severely alters the original meaning. It is somehow like a person without protection that is open to various kinds of viruses. Image immunization is a technology of protecting the images by introducing trivial perturbation, so that the protected images are immune to the viruses in that the tampered contents can be auto-recovered. Our method achieves promising results in real-world tests where experiments show accurate tamper localization as well as high-fidelity content recovery. Additionally, we show superior performance on tamper localization compared to state-of-the-art schemes based on passive forensics.
