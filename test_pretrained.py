#!/usr/bin/env python3
"""
使用预训练模型进行IMUGE测试的脚本
"""

import os
import sys
import torch
import torchvision.transforms as transforms
from torchvision import datasets
from PIL import Image
import numpy as np

# 添加源代码路径
sys.path.append('source_code_more_results_IMUGE')

from config import GlobalConfig
from network.reversible_image_net_hanson import ReversibleImageNetwork_hanson
from util import util

def check_models_exist(models_path):
    """检查预训练模型是否存在"""
    required_files = [
        'Epoch N8_prep_network.pth.tar',
        'Epoch N8_revert_network.pth.tar', 
        'Epoch N8_discriminator_patchRecovery.pth.tar',
        'Epoch N8_discriminator_patchHidden.pth.tar',
        'Epoch N1_localizer.pth.tar'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(os.path.join(models_path, file)):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少以下预训练模型文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请先运行 python download_models.py 下载模型")
        return False
    
    print("✓ 所有预训练模型文件已就绪")
    return True

def prepare_test_image(image_path, config):
    """准备测试图像"""
    transform = transforms.Compose([
        transforms.Resize((config.Height, config.Width)),
        transforms.ToTensor(),
        transforms.Normalize(mean=config.mean, std=config.std)
    ])
    
    image = Image.open(image_path).convert('RGB')
    image_tensor = transform(image).unsqueeze(0)  # 添加batch维度
    return image_tensor

def test_single_image(net, image_path, config, output_dir='test_results'):
    """测试单张图像"""
    print(f"正在测试图像: {image_path}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 准备图像
    image_tensor = prepare_test_image(image_path, config).to(config.device)
    
    # 设置为评估模式
    net.preprocessing_network.eval()
    net.revert_network.eval()
    net.localizer.eval()
    
    with torch.no_grad():
        # 进行测试
        losses, output = net.test_on_batch(image_tensor)
        x_hidden, x_recover, x_attacked, pred_label = output
        
        # 保存结果
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        
        # 保存原图
        util.save_images(image_tensor[0].cpu(),
                        f'{base_name}_original.png',
                        output_dir,
                        std=config.std,
                        mean=config.mean)
        
        # 保存隐藏图像（加了水印的图像）
        util.save_images(x_hidden[0].cpu(),
                        f'{base_name}_watermarked.png',
                        output_dir,
                        std=config.std,
                        mean=config.mean)
        
        # 保存攻击后的图像
        util.save_images(x_attacked[0].cpu(),
                        f'{base_name}_attacked.png',
                        output_dir,
                        std=config.std,
                        mean=config.mean)
        
        # 保存恢复的图像
        util.save_images(x_recover[0].cpu(),
                        f'{base_name}_recovered.png',
                        output_dir,
                        std=config.std,
                        mean=config.mean)
        
        # 保存定位结果
        util.save_images(pred_label[0].cpu(),
                        f'{base_name}_localization.png',
                        output_dir)
        
        print(f"✓ 测试完成，结果保存在 {output_dir}/")
        print(f"  - 损失值: {losses}")
        
        return losses, output

def main():
    print("=== IMUGE 预训练模型测试 ===\n")
    
    # 配置
    config = GlobalConfig()
    
    # 检查设备
    if torch.cuda.is_available():
        print(f"✓ 使用GPU: {torch.cuda.get_device_name()}")
    else:
        print("⚠ 使用CPU (速度较慢)")
        config.device = torch.device("cpu")
    
    # 检查模型文件
    if not check_models_exist(config.MODELS_PATH):
        return
    
    # 创建网络
    print("正在加载网络...")
    net = ReversibleImageNetwork_hanson(username="test", config=config)
    
    # 加载预训练模型
    try:
        print("正在加载预训练模型...")
        net.load_model(config.MODELS_PATH + 'Epoch N8')
        net.load_localizer(config.MODELS_PATH + 'Epoch N1')
        print("✓ 预训练模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 检查测试图像
    test_images = []
    if os.path.exists('sample/Test'):
        for file in os.listdir('sample/Test'):
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                test_images.append(os.path.join('sample/Test', file))
    
    if not test_images:
        print("❌ 在 sample/Test/ 目录中没有找到测试图像")
        print("请将一些 .jpg 或 .png 图像放入 sample/Test/ 目录")
        return
    
    # 测试图像
    print(f"找到 {len(test_images)} 张测试图像")
    for i, image_path in enumerate(test_images[:3]):  # 只测试前3张
        print(f"\n--- 测试图像 {i+1}/{min(3, len(test_images))} ---")
        try:
            test_single_image(net, image_path, config)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print("\n=== 测试完成 ===")
    print("查看 test_results/ 目录中的结果图像")

if __name__ == "__main__":
    main()
