# IMUGE 预训练模型测试指南

## 快速开始

### 1. 环境准备

确保安装了必要的依赖：

```bash
pip install torch torchvision torchaudio
pip install pillow numpy matplotlib opencv-python
```

### 2. 下载预训练模型

运行下载脚本：

```bash
python download_models.py
```

这会自动下载所需的预训练模型文件到 `output/models/` 目录。

### 3. 准备测试图像

将一些测试图像（.jpg 或 .png 格式）放入 `sample/Test/` 目录：

```bash
mkdir -p sample/Test
# 将你的测试图像复制到这个目录
```

### 4. 运行测试

```bash
python test_pretrained.py
```

## 测试结果说明

测试完成后，会在 `test_results/` 目录生成以下文件：

- `*_original.png` - 原始图像
- `*_watermarked.png` - 加了水印的图像（看起来和原图几乎一样）
- `*_attacked.png` - 模拟攻击后的图像（被篡改的图像）
- `*_recovered.png` - 恢复后的图像（从被篡改的图像中恢复的结果）
- `*_localization.png` - 篡改定位结果（白色区域表示检测到的篡改位置）

## 技术原理

1. **图像免疫化**：在原图中嵌入不可见的保护信息
2. **篡改检测**：自动识别图像中被修改的区域
3. **内容恢复**：将被篡改的区域恢复到原始状态

## 常见问题

### Q: 下载模型失败怎么办？
A: 可以手动从 https://github.com/dannyhe/IMUGE_pytorch 下载模型文件，放入 `output/models/` 目录。

### Q: 没有GPU怎么办？
A: 代码会自动检测并使用CPU，但速度会比较慢。

### Q: 测试图像要求？
A: 建议使用256x256或更大的RGB图像，格式为jpg或png。

### Q: 内存不足怎么办？
A: 可以修改 `source_code_more_results_IMUGE/config.py` 中的参数：
- 减小 `Height` 和 `Width`（如改为128）
- 减小 `train_batch_size`（改为1）

## 进一步开发

如果要进行进一步的研究和开发：

1. **修改网络结构**：查看 `source_code_more_results_IMUGE/network/` 目录
2. **调整损失函数**：查看 `source_code_more_results_IMUGE/loss/` 目录
3. **添加新的攻击类型**：查看 `source_code_more_results_IMUGE/noise_layers/` 目录
4. **训练新模型**：运行 `source_code_more_results_IMUGE/main_hansonRerun256.py`

## 联系方式

如有问题，可以参考原论文或GitHub仓库：
- 论文：ACMMM 2021 "From Image to Imuge: Immunized Image Generation"
- 代码：https://github.com/dannyhe/IMUGE_pytorch
