#!/usr/bin/env python3
"""
下载IMUGE预训练模型的脚本
"""

import os
import urllib.request
import zipfile
from pathlib import Path

def download_file(url, filename):
    """下载文件"""
    print(f"正在下载 {filename}...")
    try:
        urllib.request.urlretrieve(url, filename)
        print(f"✓ 下载完成: {filename}")
        return True
    except Exception as e:
        print(f"✗ 下载失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    dirs = [
        'output/models',
        'sample/Test',
        'Images/cover',
        'Images/hidden', 
        'Images/attacked',
        'Images/recovery',
        'Images/localized'
    ]
    
    for dir_path in dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✓ 创建目录: {dir_path}")

def main():
    print("=== IMUGE 预训练模型下载器 ===\n")
    
    # 创建目录
    create_directories()
    
    # GitHub仓库的预训练模型链接（注意：这些模型可能不存在）
    base_url = "https://github.com/yingqichao/From-Image-to-Imuge-Immunized-Image-Generation/raw/master/models/"
    
    # 需要下载的模型文件
    model_files = [
        "Epoch N8_prep_network.pth.tar",
        "Epoch N8_revert_network.pth.tar", 
        "Epoch N8_discriminator_patchRecovery.pth.tar",
        "Epoch N8_discriminator_patchHidden.pth.tar",
        "Epoch N1_localizer.pth.tar"
    ]
    
    print("开始下载预训练模型...")
    
    success_count = 0
    for model_file in model_files:
        url = base_url + model_file
        local_path = f"output/models/{model_file}"
        
        if download_file(url, local_path):
            success_count += 1
    
    print(f"\n下载完成! 成功下载 {success_count}/{len(model_files)} 个模型文件")

    if success_count == len(model_files):
        print("✓ 所有模型文件下载成功，可以开始测试了！")
    else:
        print("⚠ 模型文件下载失败！")
        print("\n📋 解决方案：")
        print("1. 官方仓库可能没有提供预训练模型")
        print("2. 你需要自己训练模型，或者寻找其他来源")
        print("3. 官方仓库: https://github.com/yingqichao/From-Image-to-Imuge-Immunized-Image-Generation")
        print("4. 可以尝试联系作者: <EMAIL>")
        print("\n💡 建议：")
        print("- 先运行代码理解项目结构")
        print("- 使用小数据集从头训练模型")
        print("- 或者寻找类似的预训练模型进行迁移学习")

if __name__ == "__main__":
    main()
