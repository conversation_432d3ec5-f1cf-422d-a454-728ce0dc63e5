Dear Reviewers,

Thanks for reading the codes. Before you start, we first guarantee that inside the codes we will not reveal our names so that anonymity can be assured.

Then, due to limited space for uploading, we cannot upload trained models alongside with the codes. However we have uploaded them on github.

To ensure anonymity we have invited a third person to upload the project. That means, the repo owner is not one of the co-authors of this work. Also this guy won't tell you who we are.

The models are available at https://github.com/dannyhe/IMUGE_pytorch

In case you train the models on your own from scratch, run main_hansonRerun256.py. You need to change the directory of ILSVRC2017 via modifying self.TRAIN_PATH in config.py

To test the images, we have also uploaded the rest of the 134 testing results in the above github repo.

Again thanks for reading the material!